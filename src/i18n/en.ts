// in src/i18n/en.js
import englishMessages from 'ra-language-english'

export const en = {
  ...englishMessages,
  common: {
    group: {
      userInfo: 'User Info',
      residencyInfo: 'Residency Info',
      kycInfo: 'KYC Info',
      referralInfo: 'Referral Info',
    },
    label: {
      firstName: 'First Name',
      lastName: 'Last Name',
      dateOfBirth: 'Date of Birth',
      age: 'Age - Months young',
      email: 'Email',
      verifiedEmail: 'Verified email',
      phoneNumber: 'Phone Number',
      verifiedNumber: 'Verified number',
      sex: 'Sex',
      investmentAccountStatus: 'Investment Account Status',
      residencyCountry: 'Residency Country',
      l0Status: 'L0 Status',
      l1Status: 'L1 Status',
      l2Status: 'L2 Status',
      referralCode: 'Referral Code',
      referredBy: 'Referred By',
    },
    error: {
      noRecord: 'Could not get record',
      genericError: 'Something went wrong',
      imageError: 'Error loading image, please try refreshing',
    },
    feedback: {
      confirmReview:
        'I have thoroughly reviewed this submission, and will be held accountable if incorrect.',
      approveSubmission: 'Are you sure you want to approve: %{email}?',
      documentApproved: 'Document approved',
      documentDenied: 'Document denied: %{email}',
    },
    button: {
      closeAction: 'Close',
      approveDocument: 'Approve document',
      denyDocument: 'Deny document',
    },
  },
  authentication: {
    label: {
      tontineEmail: 'Your tontine email',
    },
    button: {
      bioEnroll: 'Start face scan',
    },
    error: {
      genericFaceScanError: 'Face scan failed, please try again',
    },
  },
  operator: {
    label: {
      resendEmail: 'Resend email',
    },
  },
  kyc: {
    label: {
      verifiedName: 'Verified name',
      from: 'From',
      to: 'To',
      status: 'Status',
      submitted: 'Submitted',
      analysis: 'Analysis',
      approved: 'Approved',
      denied: 'Denied',
    },
    guideMessage: {
      noAutoFaceScan:
        'If face scan failed to start automatically, click the button below to start it manually.',
    },
    addressReview: {
      label: {
        addressLine1: 'Address Line 1',
        addressLine2: 'Address Line 2',
        postalCode: 'Postal Code',
        city: 'City',
        state: 'State',
        country: 'Country',
        addressSubmissionDate: 'Submission Time',
        denyTextReason: 'Deny reason',
        denyNameMismatch: 'Name mismatch',
        denyDocTooOld: 'Document too old',
        denyIllegibleDoc: 'Illegible document',
        denyDocumentNotShown: 'Address not shown',
        denyOtherReason: 'Other reason(comment below)',
        denyReasonRequired: 'Reason is required for this type',
      },
      group: {
        addressSubmission: 'Address Submission',
        addressDocument: 'Submitted document',
        reviewDocument: 'Review document',
      },
    },
    idReview: {
      label: {
        verifiedId: 'Verified ID',
        idReviewStatus: 'ID Review Status',
        idRejectionReason: 'ID Rejection Reason',
        documentType: 'Document Type',
        documentNumber: 'Document Number',
        issuingCountry: 'Issuing Country',
        firstName: 'First Name',
        lastName: 'Last Name',
        dateOfBirth: 'Date of Birth',
        issueDate: 'Issue Date',
        expiryDate: 'Expiry Date',
        idSubmissionDate: 'Submission Time',
        denyTextReason: 'Deny reason',
        denyNameMismatch: 'Name mismatch',
        denyDocTooOld: 'Document too old',
        denyIllegibleDoc: 'Illegible document',
        denyDocumentNotShown: 'Document not shown',
        denyOtherReason: 'Other reason(comment below)',
        denyReasonRequired: 'Reason is required for this type',
        barcodeValues: 'Barcode Values',
        nfcValues: 'NFC Values',
        scannedValues: 'Scanned Values',
        templateInfo: 'Template Info',
        templateName: 'Template Name',
        templateType: 'Template Type',
        userConfirmedValues: 'User Confirmed Values',
      },
      group: {
        idSubmission: 'ID Submission',
        idDocument: 'Submitted document',
        reviewDocument: 'Review document',
        userConfirmedValues: 'User confirmed values',
        scannedValues: 'Scanned values',
        rawDocumentData: 'Raw Document Data',
      },
    },
  },
  menu: {
    tontineUsers: 'User Search',
    addressReviewQueue: 'Address Review Queue',
    companyOperators: 'Company Operators',
    idDocumentReviewQueue: 'ID Document Review Queue',
  },
  dashboard: {
    title: 'Welcome to the Internal Dashboard',
    topBarTitle: 'Tontine Internal Dashboard',
    subtitle: 'Access core platform management areas.',
    cards: {
      idDocumentReviewQueue: {
        subtitle: 'ID Document Review Queue',
        buttonText: 'Access ID Queue',
      },
      tontineUsers: {
        subtitle: 'Identity Verification Queue',
        buttonText: 'Access users',
      },
      addressReviewQueue: {
        subtitle: 'Address Verification Queue',
        buttonText: 'Access Address Queue',
      },
      companyOperators: {
        subtitle: 'Browse all Tontine Operators',
        buttonText: 'Access operators',
      },
      userSearch: {
        title: 'User Search',
        subtitle: 'Find individual users',
        buttonText: 'Search Users',
      },
    },
  },
} as const
