import { Divider, Typography } from '@mui/material'
import { FunctionField, SimpleShowLayout, TextField } from 'react-admin'
import { KYCStatusField } from '../../../common/components/KYCStatusField'
import { en } from '../../../i18n/en'

export const AllUserDetails = () => {
  return (
    <>
      <Typography>{en.common.group.userInfo}</Typography>
      <Divider />
      <SimpleShowLayout direction='row' spacing={10}>
        <FunctionField
          label={en.common.label.firstName}
          render={(record) =>
            record.verified_first_name ?? record.unverified_first_name ?? '-'
          }
        />
        <FunctionField
          label={en.common.label.lastName}
          render={(record) =>
            record.verified_last_name ?? record.unverified_last_name ?? '-'
          }
        />
        <FunctionField
          label={en.common.label.dateOfBirth}
          render={(record) =>
            record.verified_date_of_birth ??
            record.unverified_date_of_birth ??
            '-'
          }
        />
        <FunctionField
          label={en.common.label.age}
          render={(record) =>
            record.verified_age
              ? `${record.verified_age.age} years, ${record.verified_age.month} months`
              : // biome-ignore lint/style/noNestedTernary: <Cleanest way to do this>
                record.unverified_age
                ? `${record.unverified_age.age} years, ${record.unverified_age.month} months`
                : '-'
          }
        />
        <FunctionField
          label={en.common.label.phoneNumber}
          render={(record) =>
            record.verified_phone_number ??
            record.unverified_phone_number ??
            '-'
          }
        />
        <FunctionField
          label={en.common.label.sex}
          render={(record) =>
            record.verified_sex ?? record.unverified_sex ?? '-'
          }
        />
        <TextField source='email' label={en.common.label.email} />
        <TextField
          source='investment_account_status'
          label={en.common.label.investmentAccountStatus}
        />
      </SimpleShowLayout>
      <Typography>{en.common.group.residencyInfo}</Typography>
      <Divider />
      <SimpleShowLayout direction='row' spacing={10}>
        <FunctionField
          label={en.common.label.residencyCountry}
          render={(record) =>
            record.verified_residency ?? record.unverified_residency ?? '-'
          }
        />
      </SimpleShowLayout>
      <Typography>{en.common.group.kycInfo}</Typography>
      <Divider />
      <SimpleShowLayout direction='row' spacing={10}>
        <TextField
          source='id_review_status'
          label={en.kyc.idReview.label.idReviewStatus}
        />
        <TextField
          source='id_rejection_reason'
          label={en.kyc.idReview.label.idRejectionReason}
        />
        <KYCStatusField level='L0' label={en.common.label.l0Status} />
        <KYCStatusField level='L1' label={en.common.label.l1Status} />
        <KYCStatusField level='L2' label={en.common.label.l2Status} />
      </SimpleShowLayout>
      <Typography>{en.common.group.referralInfo}</Typography>
      <Divider />
      <SimpleShowLayout direction='row' spacing={10}>
        <TextField
          source='referral_code_created'
          label={en.common.label.referralCode}
        />
        <TextField
          source='referred_code_redeemed'
          label={en.common.label.referredBy}
        />
      </SimpleShowLayout>
    </>
  )
}
