import {
  <PERSON>oleanField,
  <PERSON>grid,
  FunctionField,
  List,
  SearchInput,
  TextField,
} from 'react-admin'
import { en } from '../../../i18n/en'

export const TontineUsersList = () => {
  return (
    <List
      perPage={25}
      queryOptions={{
        refetchInterval: 20_000,
      }}
      filters={[<SearchInput key={'firstName'} source='~firstName' alwaysOn />]}
    >
      <Datagrid rowClick='show' bulkActionButtons={false}>
        <FunctionField
          label={en.common.label.firstName}
          render={(record) =>
            record.verified_first_name ?? record.unverified_first_name ?? '-'
          }
        />
        <FunctionField
          label={en.common.label.lastName}
          render={(record) =>
            record.verified_last_name ?? record.unverified_last_name ?? '-'
          }
        />

        <FunctionField
          label={en.common.label.dateOfBirth}
          render={(record) => {
            const date =
              record.verified_date_of_birth ??
              record.unverified_date_of_birth ??
              null
            return date ? new Date(date).toLocaleDateString('en-GB') : '-'
          }}
        />
        <FunctionField
          label={en.common.label.age}
          render={(record) =>
            `${record.verified_age?.age ?? record.unverified_age?.age ?? '-'} years, 
            ${record.verified_age?.month ?? record.unverified_age?.month ?? '-'} months`
          }
        />
        <BooleanField
          source='kyc_status.L1.requirements.id_verified'
          label={en.kyc.idReview.label.verifiedId}
        />
        <TextField source='email' label={en.common.label.email} />
        <BooleanField
          source='kyc_status.L0.requirements.email_verified'
          label={en.common.label.verifiedEmail}
        />
        <FunctionField
          label={en.common.label.phoneNumber}
          render={(record) =>
            record.verified_phone_number ??
            record.unverified_phone_number ??
            '-'
          }
        />
        <BooleanField
          source='kyc_status.L1.requirements.phone_verified'
          label={en.common.label.verifiedNumber}
        />
      </Datagrid>
    </List>
  )
}
