import { CircularProgress } from '@mui/material'
import { useRecordContext } from 'react-admin'
import { resources } from '../resources'
import { CustomImageFetch } from './CustomImageFetch'

/**
 * Fetches the document proof image for the address review using `proof_id` key
 */
const AddressDocument = () => {
  const record = useRecordContext()

  if (!record?.id) {
    return <CircularProgress />
  }

  return (
    <CustomImageFetch
      id={`\?proofId\=${record?.address_submission?.user_submitted_address?.proof_id}`}
      resource={resources.addressProofDocument}
    />
  )
}

export { AddressDocument }
