import { SimpleShowLayout, TextField } from 'react-admin'
import { en } from '../../../i18n/en'

export const RawDocumentData = () => {
  return (
    <SimpleShowLayout direction='row' spacing={10}>
      <TextField
        source='raw_document_data.barcode_values'
        label={en.kyc.idReview.label.barcodeValues}
      />

      <TextField
        source='raw_document_data.nfc_values'
        label={en.kyc.idReview.label.nfcValues}
      />

      <TextField
        source='raw_document_data.user_confirmed_values'
        label={en.kyc.idReview.label.userConfirmedValues}
      />

      <TextField
        source='raw_document_data.template_info.template_name'
        label={en.kyc.idReview.label.templateName}
      />

      <TextField
        source='raw_document_data.template_info.template_type'
        label={en.kyc.idReview.label.templateType}
      />
    </SimpleShowLayout>
  )
}
