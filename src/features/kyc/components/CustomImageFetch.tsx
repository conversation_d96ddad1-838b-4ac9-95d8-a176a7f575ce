import { Box, CircularProgress, Typography } from '@mui/material'
import { useGetOne } from 'react-admin'
import { en } from '../../../i18n/en'
import { CustomImageFetchProps } from '../types/CustomImageFetch.types'

/**
 * Component for fetching and displaying images from the API
 */
export const CustomImageFetch = ({
  id,
  resource,
  alt = 'Fetched image',
  width = 1000,
  height = 500,
}: CustomImageFetchProps) => {
  const { data, isLoading, error } = useGetOne(
    resource,
    {
      id,
    },
    {
      retry: 2,
      staleTime: 30000,
      // Cache for 30 seconds
    }
  )

  if (isLoading) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        width={width}
        height={height}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (error || !data?.document) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        width={width}
        height={height}
      >
        <Typography color='error'>{en.common.error.imageError}</Typography>
      </Box>
    )
  }

  if (data.document_mime_type === 'application/pdf') {
    return (
      <embed
        src={`data:${data.document_mime_type};base64, ${data.document}`}
        width={width}
        height={height}
        type='application/pdf'
      />
    )
  }

  return (
    <img
      src={`data:${data.document_mime_type};base64, ${data.document}`}
      alt={alt}
      width={width}
      height={height}
      style={{ objectFit: 'contain' }}
    />
  )
}
