import { en } from '../../../i18n/en'

const addressDenyOptions = [
  {
    id: 'NAME_MISMATCH',
    name: en.kyc.addressReview.label.denyNameMismatch,
  },
  {
    id: 'DOCUMENT_TOO_OLD',
    name: en.kyc.addressReview.label.denyDocTooOld,
  },
  {
    id: 'ILLEGIBLE_DOCUMENT',
    name: en.kyc.addressReview.label.denyIllegibleDoc,
  },
  {
    id: 'ADDRESS_NOT_SHOWN',
    name: en.kyc.addressReview.label.denyDocumentNotShown,
  },
  {
    id: 'OTHER_REASON',
    name: en.kyc.addressReview.label.denyOtherReason,
  },
]

const idDocumentDenyOptions = [
  {
    id: 'NAME_MISMATCH',
    name: en.kyc.idReview.label.denyNameMismatch,
  },
  {
    id: 'DOCUMENT_TOO_OLD',
    name: en.kyc.idReview.label.denyDocTooOld,
  },
  {
    id: 'ILLEGIBLE_DOCUMENT',
    name: en.kyc.idReview.label.denyIllegibleDoc,
  },
  {
    id: 'DOCUMENT_NOT_SHOWN',
    name: en.kyc.idReview.label.denyDocumentNotShown,
  },
  {
    id: 'OTHER_REASON',
    name: en.kyc.idReview.label.denyOtherReason,
  },
]

export { addressDenyOptions, idDocumentDenyOptions }
