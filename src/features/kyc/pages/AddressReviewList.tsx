import { Datagrid, DateInput, List, SelectInput } from 'react-admin'
import { DocumentSubmissionStatus } from '../../../common/types/DocumentSubmissionStatus.types'
import { en } from '../../../i18n/en'
import { UserAddressDetails } from '../components/UserAddressDetails'
import { UserDetails } from '../components/UserDetails'

const statusColors: Record<DocumentSubmissionStatus, string> = {
  SUBMITTED: '6px solid orange',
  ANALYSIS: '6px solid orange',
  APPROVED: '6px solid green',
  DENIED: '6px solid red',
} as const

export const AddressReviewList = () => {
  return (
    <List
      perPage={25}
      queryOptions={{
        refetchInterval: 20_000,
      }}
      filters={[
        <DateInput
          key={'GTE_submittedAt'}
          source='GTE_submittedAt'
          label={en.kyc.label.from}
          alwaysOn
        />,
        <DateInput
          key={'LTE_submittedAt'}
          source='LTE_submittedAt'
          label={en.kyc.label.to}
          alwaysOn
        />,
        <SelectInput
          key={'status'}
          source='status'
          label={en.kyc.label.status}
          alwaysOn
          choices={[
            {
              id: 'SUBMITTED',
              name: `${en.kyc.label.submitted} 🟠`,
            },
            {
              id: 'ANALYSIS',
              name: `${en.kyc.label.analysis} 🟠`,
            },
            {
              id: 'APPROVED',
              name: `${en.kyc.label.approved} 🟢`,
            },
            {
              id: 'DENIED',
              name: `${en.kyc.label.denied} 🔴`,
            },
          ]}
        />,
      ]}
    >
      <Datagrid
        rowClick='show'
        bulkActionButtons={false}
        rowSx={(record) => ({
          borderLeft:
            statusColors[
              (record.address_submission?.status as DocumentSubmissionStatus) ??
                'SUBMITTED'
            ],
        })}
      >
        <UserDetails />
        <UserAddressDetails />
      </Datagrid>
    </List>
  )
}
