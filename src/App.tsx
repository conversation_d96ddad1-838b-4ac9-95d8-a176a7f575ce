import MapIcon from '@mui/icons-material/Map'
import PeopleIcon from '@mui/icons-material/People'
import SupervisedUserCircle from '@mui/icons-material/SupervisedUserCircle'
import { Admin, Resource } from 'react-admin'
import { Dashboard } from './Dashboard'
import { authProvider } from './features/authentication/authProvider'
import { LoginPage } from './features/authentication/pages/LoginPage'
import { dataProvider } from './features/dataProvider'
import { AddressInfoShow } from './features/kyc/pages/AddressInfoShow'
import { AddressReviewList } from './features/kyc/pages/AddressReviewList'
import { resources } from './features/kyc/resources'
import { TontineUserShow } from './features/mt-user-management/pages/TontineUserShow'
import { TontineUsersList } from './features/mt-user-management/pages/TontineUsersList'
import { resources as tontineUserResources } from './features/mt-user-management/resources'
import { OperatorsPage } from './features/operator/pages/OperatorsPage'
import { OperatorsPageShow } from './features/operator/pages/OperatorsPageShow'
import { resources as operatorResources } from './features/operator/resources'
import { en } from './i18n/en'
import { Layout } from './Layout'
import { IdDocumentInfoShow } from './features/kyc/pages/IdDocumentInfoShow'
import { IdDocumentReviewList } from './features/kyc/pages/IdDocumentReviewList'

export const App = () => (
  <Admin
    loginPage={LoginPage}
    layout={Layout}
    disableTelemetry
    authProvider={authProvider}
    dataProvider={dataProvider}
    dashboard={Dashboard}
  >
    <Resource
      show={AddressInfoShow}
      list={AddressReviewList}
      icon={MapIcon}
      name={resources.addressReviewListAll}
      options={{
        label: en.menu.addressReviewQueue,
      }}
    />
    <Resource
      list={OperatorsPage}
      show={OperatorsPageShow}
      icon={PeopleIcon}
      name={operatorResources.operatorsListAll}
      options={{
        label: en.menu.companyOperators,
      }}
    />
    <Resource
      list={TontineUsersList}
      show={TontineUserShow}
      icon={SupervisedUserCircle}
      name={tontineUserResources.tontineUsersAll}
      options={{
        label: en.menu.tontineUsers,
      }}
    />
    <Resource
      list={IdDocumentReviewList}
      show={IdDocumentInfoShow}
      icon={MapIcon}
      name={resources.idDocumentListAll}
      options={{
        label: en.menu.idDocumentReviewQueue,
      }}
    />
  </Admin>
)
