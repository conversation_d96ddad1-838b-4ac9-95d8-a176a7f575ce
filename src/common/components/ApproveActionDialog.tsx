import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import { Dialog, DialogContentText, DialogTitle, Stack } from '@mui/material'
import { useState } from 'react'
import {
  Button,
  useNotify,
  useRecordContext,
  useRedirect,
  useTranslate,
  useUpdate,
} from 'react-admin'
import { ApproveActionDialogProps } from '../../features/kyc/types/ApproveActionDialog.types'
import { en } from '../../i18n/en'
import { autoHideDuration } from '../utils/consts'
import { getNestedValue } from '../utils/utils'

/**
 * Works with approve action APIs, the user id is applied in the request
 */
export const ApproveActionDialog = ({
  redirectTo,
  resource,
  approvedActionMsg,
  recordKey,
}: ApproveActionDialogProps) => {
  const [openDialog, setOpenDialog] = useState(false)
  const translate = useTranslate()
  const record = useRecordContext()
  const redirect = useRedirect()
  const notify = useNotify()

  const [approve, { isPending }] = useUpdate(
    resource,
    {
      // On purpose empty string, for backend support!
      id: '',
      data: {
        id: getNestedValue({ record, path: recordKey }),
      },
    },
    {
      onSuccess: () => {
        notify(approvedActionMsg ?? en.common.feedback.documentApproved, {
          type: 'success',
          autoHideDuration,
        })
        setOpenDialog(false)
        redirect(redirectTo ?? '/')
      },
      onError: (error) => {
        notify(`${en.common.error.genericError}: ${error}`, {
          type: 'error',
          autoHideDuration,
        })
      },
    }
  )

  return (
    <>
      <Button
        label={en.common.button.approveDocument}
        onClick={() => setOpenDialog(true)}
        color='success'
      >
        <CheckCircleIcon />
      </Button>
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <Stack spacing={3} p={2}>
          <DialogTitle
            sx={{
              padding: '0',
            }}
          >
            {translate(en.common.feedback?.approveSubmission, {
              email: record?.user_details?.email,
            })}
          </DialogTitle>
          <DialogContentText>
            {en.common.feedback?.confirmReview}
          </DialogContentText>
          <Stack
            direction='row'
            spacing={5}
            width={'100%'}
            justifyContent={'flex-end'}
          >
            <Button
              onClick={() => setOpenDialog(false)}
              label={en.common.button.closeAction}
              disabled={isPending}
            >
              <ArrowBackIosIcon />
            </Button>
            <Button
              onClick={() =>
                approve(resource, {
                  data: {
                    id: record?.user_details?.user_id,
                  },
                })
              }
              label={en.common.button.approveDocument}
              loading={isPending}
              color='success'
              variant='contained'
            >
              <CheckCircleIcon />
            </Button>
          </Stack>
        </Stack>
      </Dialog>
    </>
  )
}
