{"private": true, "type": "module", "scripts": {"dev": "./scripts/prepare.sh && npm run type-check && npm run lint && vite", "build": "./scripts/prepare.sh && vite build", "serve": "vite preview", "type-check": "tsc --noEmit", "lint": "biome lint", "format": "biome format", "check": "biome check", "check:fix": "biome check --fix"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.2.0", "ra-data-simple-rest": "^5.8.3", "react": "^19.1.0", "react-admin": "^5.8.1", "react-dom": "^19.1.0", "react-router": "^7.6.0", "react-router-dom": "^7.6.0"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "typescript": "^5.8.3", "vite": "^6.3.5"}, "name": "internal-dashboard"}